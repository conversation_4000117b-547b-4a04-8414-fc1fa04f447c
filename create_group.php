<?php
session_start();
if (!isset($_SESSION['user_id'])) {
    header("Location: login.php");
    exit();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create Group</title>
    <link rel="stylesheet" href="styles.css"> <!-- Link to your custom CSS -->
</head>
<body>
    <header>
        <h1>Create a New Group</h1>
    </header>
    <nav>
        <ul ALIGN="center">
            <li><a href="join_group.php">Join Group</a></li>
            <li><a href="create_group.php">Create Group</a></li>
            <li><a href="addgrp_expense.php">Add Group Expense</a></li>
            <li><a href="view_members.php">View Group Member</a></li>
            <li><a href="view_group_summary.php">Group Summary</a></li>
            <li><a href="main_dash.php">Main Dashboard</a></li>
</UL>
            <div class="profile">
            <form action="logout.php" method="POST" style="display: inline;">
                <button type="submit" class="logout-btn">Logout</button>
            </form>
            </div>
    </nav>
    <main>
        <form action="create_group_action.php" method="POST" >
            <div class="form-group">
                <label for="group_name">Group Name:</label>
                <input type="text" name="group_name" id="group_name" required>
            </div>
            <div>
                <button type="submit" class="btn">Create Group</button>
            </div>
        </form>
          <!-- Display Invite Link if Available -->
        <?php if (isset($_SESSION['invite_link'])): ?>
            <div class="invite_link">
                Invite Link: 
                <input type="text" id="copyinvitelink" value="<?= $_SESSION['invite_link'] ?>" readonly>
                <button class="copy-btn" onclick="copyInviteLink()">Copy Link</button>
            </div>
            <?php unset($_SESSION['invite_link']); // Clear the invite link after displaying ?>
        <?php endif; ?>

        <a href="group_dashboard.php">Back to Dashboard</a>
    </main>
</body>
</html>