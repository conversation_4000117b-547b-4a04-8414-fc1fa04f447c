<?php
session_start();

// Check if user is logged in
$is_logged_in = isset($_SESSION['user_id']);
$user_role = $_SESSION['role'] ?? null;

// Handle form submission
$message_sent = false;
$error_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = trim($_POST['name'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $subject = trim($_POST['subject'] ?? '');
    $message = trim($_POST['message'] ?? '');
    
    // Basic validation
    if (empty($name) || empty($email) || empty($subject) || empty($message)) {
        $error_message = 'All fields are required.';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error_message = 'Please enter a valid email address.';
    } else {
        // Here you would typically save to database or send email
        // For now, we'll just show a success message
        $message_sent = true;
        
        // Optional: Save to database or send email
        // You can add database logic here to store contact messages
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contact Us - Expense Tracker</title>
    <link rel="stylesheet" href="style.css">
    <style>
        .contact-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: whitesmoke;
            border-radius: 10px;
            box-shadow: 0 2px 6px rgba(0,0,0,0.5);
            margin-top: 120px;
            margin-bottom: 40px;
        }
        
        .contact-header {
            text-align: center;
            margin-bottom: 40px;
            color: #2c3e50;
        }
        
        .contact-header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            background: linear-gradient(135deg, #2c3e50, #4ca1af);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .contact-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            margin-bottom: 40px;
        }
        
        .contact-form {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .contact-info {
            background: linear-gradient(135deg, #2c3e50, #4ca1af);
            color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #2c3e50;
        }
        
        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #e0e0e0;
            border-radius: 6px;
            font-size: 16px;
            transition: border-color 0.3s ease;
            box-sizing: border-box;
        }
        
        .form-group input:focus,
        .form-group textarea:focus,
        .form-group select:focus {
            outline: none;
            border-color: #4ca1af;
            box-shadow: 0 0 0 3px rgba(76, 161, 175, 0.1);
        }
        
        .form-group textarea {
            resize: vertical;
            min-height: 120px;
        }
        
        .submit-btn {
            background: linear-gradient(135deg, #2c3e50, #4ca1af);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 6px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
        }
        
        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(76, 161, 175, 0.3);
        }
        
        .contact-info h3 {
            margin-bottom: 20px;
            font-size: 1.5rem;
        }
        
        .info-item {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            padding: 15px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            transition: background 0.3s ease;
        }
        
        .info-item:hover {
            background: rgba(255, 255, 255, 0.2);
        }
        
        .info-icon {
            width: 40px;
            height: 40px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-size: 18px;
        }
        
        .info-text h4 {
            margin: 0 0 5px 0;
            font-size: 1.1rem;
        }
        
        .info-text p {
            margin: 0;
            opacity: 0.9;
        }
        
        .success-message {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
            border: 1px solid #c3e6cb;
        }
        
        .error-message {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
            border: 1px solid #f5c6cb;
        }
        
        .faq-section {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            margin-top: 40px;
        }
        
        .faq-item {
            margin-bottom: 20px;
            border-bottom: 1px solid #e0e0e0;
            padding-bottom: 20px;
        }
        
        .faq-item:last-child {
            border-bottom: none;
            margin-bottom: 0;
            padding-bottom: 0;
        }
        
        .faq-question {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 1.1rem;
        }
        
        .faq-answer {
            color: #666;
            line-height: 1.6;
        }
        
        @media (max-width: 768px) {
            .contact-content {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .contact-container {
                margin-top: 100px;
                padding: 15px;
            }
            
            .contact-form,
            .contact-info {
                padding: 20px;
            }
            
            .contact-header h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <header>
        <h2>Expense Tracker</h2>
    </header>
    
    <nav>
        <div class="logo" style="display: inline-block;">
            <img src="spendify.jpg" alt="Logo" width="50" height="50">Expense Tracker
        </div>
        <ul>
            <?php if ($is_logged_in): ?>
                <li><a href="personal/personal_dashboard.php">Personal Expense</a></li>
                <li><a href="group/group_dashboard.php">Group Expenses</a></li>
            <?php else: ?>
                <li><a href="login.php">Login</a></li>
                <li><a href="register.php">Register</a></li>
            <?php endif; ?>
            <li><a href="aboutus.php">About Us</a></li>
            <li><a href="contact.php">Contact Us</a></li>
            <?php if ($is_logged_in): ?>
                <li><a href="logout.php" class="logout-btn">Logout</a></li>
            <?php endif; ?>
        </ul>
    </nav>

    <main class="container">
        <div class="contact-container">
            <div class="contact-header">
                <h1>Contact Us</h1>
                <p>We'd love to hear from you. Send us a message and we'll respond as soon as possible.</p>
            </div>
            
            <div class="contact-content">
                <!-- Contact Form -->
                <div class="contact-form">
                    <h3 style="color: #2c3e50; margin-bottom: 20px;">Send us a Message</h3>
                    
                    <?php if ($message_sent): ?>
                        <div class="success-message">
                            <strong>Thank you!</strong> Your message has been sent successfully. We'll get back to you soon.
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($error_message): ?>
                        <div class="error-message">
                            <strong>Error:</strong> <?= htmlspecialchars($error_message) ?>
                        </div>
                    <?php endif; ?>
                    
                    <form method="POST" action="contact.php">
                        <div class="form-group">
                            <label for="name">Full Name *</label>
                            <input type="text" id="name" name="name" required 
                                   value="<?= htmlspecialchars($_POST['name'] ?? '') ?>">
                        </div>
                        
                        <div class="form-group">
                            <label for="email">Email Address *</label>
                            <input type="email" id="email" name="email" required 
                                   value="<?= htmlspecialchars($_POST['email'] ?? '') ?>">
                        </div>
                        
                        <div class="form-group">
                            <label for="subject">Subject *</label>
                            <select id="subject" name="subject" required>
                                <option value="">Select a subject</option>
                                <option value="general" <?= ($_POST['subject'] ?? '') === 'general' ? 'selected' : '' ?>>General Inquiry</option>
                                <option value="support" <?= ($_POST['subject'] ?? '') === 'support' ? 'selected' : '' ?>>Technical Support</option>
                                <option value="feature" <?= ($_POST['subject'] ?? '') === 'feature' ? 'selected' : '' ?>>Feature Request</option>
                                <option value="bug" <?= ($_POST['subject'] ?? '') === 'bug' ? 'selected' : '' ?>>Bug Report</option>
                                <option value="billing" <?= ($_POST['subject'] ?? '') === 'billing' ? 'selected' : '' ?>>Billing Question</option>
                                <option value="other" <?= ($_POST['subject'] ?? '') === 'other' ? 'selected' : '' ?>>Other</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="message">Message *</label>
                            <textarea id="message" name="message" required 
                                      placeholder="Please describe your inquiry in detail..."><?= htmlspecialchars($_POST['message'] ?? '') ?></textarea>
                        </div>
                        
                        <button type="submit" class="submit-btn">Send Message</button>
                    </form>
                </div>
                
                <!-- Contact Information -->
                <div class="contact-info">
                    <h3>Get in Touch</h3>
                    
                    <div class="info-item">
                        <div class="info-icon">📧</div>
                        <div class="info-text">
                            <h4>Email</h4>
                            <p><EMAIL></p>
                        </div>
                    </div>
                    
                    <div class="info-item">
                        <div class="info-icon">📞</div>
                        <div class="info-text">
                            <h4>Phone</h4>
                            <p>+91 98765 43210</p>
                        </div>
                    </div>
                    
                    <div class="info-item">
                        <div class="info-icon">📍</div>
                        <div class="info-text">
                            <h4>Address</h4>
                            <p>123 Tech Street<br>Mumbai, Maharashtra 400001<br>India</p>
                        </div>
                    </div>
                    
                    <div class="info-item">
                        <div class="info-icon">🕒</div>
                        <div class="info-text">
                            <h4>Business Hours</h4>
                            <p>Monday - Friday: 9:00 AM - 6:00 PM<br>Saturday: 10:00 AM - 4:00 PM<br>Sunday: Closed</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- FAQ Section -->
            <div class="faq-section">
                <h3 style="color: #2c3e50; margin-bottom: 30px; text-align: center;">Frequently Asked Questions</h3>
                
                <div class="faq-item">
                    <div class="faq-question">How do I reset my password?</div>
                    <div class="faq-answer">You can reset your password by clicking the "Forgot Password" link on the login page. We'll send you an email with instructions to create a new password.</div>
                </div>
                
                <div class="faq-item">
                    <div class="faq-question">Can I export my expense data?</div>
                    <div class="faq-answer">Yes! You can export your expense data from the Reports section. We support CSV and PDF formats for easy data portability.</div>
                </div>
                
                <div class="faq-item">
                    <div class="faq-question">How do I create a group for shared expenses?</div>
                    <div class="faq-answer">Navigate to the Group Expenses section and click "Create Group". You can then invite members using the generated join link.</div>
                </div>
                
                <div class="faq-item">
                    <div class="faq-question">Is my financial data secure?</div>
                    <div class="faq-answer">Absolutely! We use industry-standard encryption and security measures to protect your data. Your information is never shared with third parties.</div>
                </div>
                
                <div class="faq-item">
                    <div class="faq-question">Can I use the app on mobile devices?</div>
                    <div class="faq-answer">Yes, our expense tracker is fully responsive and works great on mobile devices, tablets, and desktops.</div>
                </div>
            </div>
        </div>
    </main>

    <?php include "footer.php"; ?>
</body>
</html>
